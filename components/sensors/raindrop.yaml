substitutions:
    raindrop_sensor_output_pin: GPIO15
    raindrop_sensor_ao_pin: GPIO39
    raindrop_sensor_startup_delay: 100ms
    raindrop_sensor_update_interval: 5s

globals:
    - id: is_raindrop_sensor_updating
      type: bool
      restore_value: false
      initial_value: 'false'

esphome:
    on_boot:
        - button.press: update_raindrop_sensor_button

output:
    - platform: gpio
      id: raindrop_sensor_output
      pin:
          number: $raindrop_sensor_output_pin
          ignore_strapping_warning: true

switch:
    - platform: output
      id: raindrop_sensor_switch
      internal: true
      restore_mode: ALWAYS_OFF
      output: raindrop_sensor_output

sensor:
    - platform: adc
      id: raindrop_sensor_voltage
      internal: true
      update_interval: never
      pin: $raindrop_sensor_ao_pin
      attenuation: 12db

    - platform: adc
      id: raindrop_sensor_voltage_display
      name: 'Raindrop Sensor Voltage'
      icon: 'mdi:flash'
      device_class: voltage
      unit_of_measurement: 'V'
      accuracy_decimals: 2
      update_interval: never
      pin: $raindrop_sensor_ao_pin
      attenuation: 12db
      entity_category: diagnostic

binary_sensor:
    - platform: analog_threshold
      id: is_raining
      name: 'Raining'
      icon: 'mdi:weather-lightning-rainy'
      device_class: moisture
      sensor_id: raindrop_sensor_voltage
      threshold:
          upper: 2.8
          lower: 2.5

button:
    - platform: template
      id: update_raindrop_sensor_button
      name: 'Update Raindrop Sensor'
      icon: 'mdi:cloud-refresh-variant'
      entity_category: diagnostic
      disabled_by_default: true
      on_press:
          - if:
                condition:
                    lambda: 'return id(is_raindrop_sensor_updating) == false;'
                then:
                    - globals.set:
                          id: is_raindrop_sensor_updating
                          value: 'true'
                    - switch.turn_on: raindrop_sensor_switch
                    - delay: $raindrop_sensor_startup_delay
                    - component.update: raindrop_sensor_voltage
                    - component.update: raindrop_sensor_voltage_display
                    - delay: $raindrop_sensor_startup_delay
                    - switch.turn_off: raindrop_sensor_switch
                    - globals.set:
                          id: is_raindrop_sensor_updating
                          value: 'false'

interval:
    - interval: $raindrop_sensor_update_interval
      then:
          - button.press: update_raindrop_sensor_button
